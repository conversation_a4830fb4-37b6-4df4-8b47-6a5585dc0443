#!/usr/bin/env python3
"""
OMOP Vocabulary Loader Script - Official OHDSI Pattern Implementation

This script loads OMOP vocabularies from Athena CSV files into PostgreSQL following
the official OHDSI pattern validated by community implementations.

Official OHDSI Pattern Based On:
- OHDSI/ETL-Synthea: https://github.com/OHDSI/ETL-Synthea/blob/main/R/LoadVocabFromCsv.r
- sidataplus community: https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py
- OHDSI Forums Official Guidance: https://forums.ohdsi.org/t/foreign-key-constraints-issue/20462

Technical Approach (Official OHDSI Pattern):
- Uses DELETE FROM + INSERT pattern (same as official R method)
- NO constraint disabling required (follows official OHDSI guidance)
- Uses pandas chunking with execute_values() for performance
- Handles circular dependencies through DELETE + INSERT pattern

Key Differences from Previous Implementation:
- ✅ Follows official OHDSI constraint handling pattern
- ✅ Uses execute_values() instead of COPY (community standard)
- ✅ No session_replication_role manipulation required
- ✅ Works with regular database user (no superuser required)

Usage:
    python scripts/load_vocabularies.py

Requirements:
    - Athena vocabulary files in data/vocabulary/omop_v5_* directory
    - PostgreSQL OMOP database configured in .env
    - pandas and psycopg2-binary packages installed

Configuration:
    - Set VOCABULARY_PATH in .env to specify exact directory, or
    - Script will auto-detect most recent omop_v5_* directory in data/vocabulary/
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
import pandas as pd
import numpy as np
import psycopg2
import psycopg2.extras

# Load environment variables
load_dotenv()

# Database configuration
# Note: Using regular OMOP user (no superuser required for official OHDSI pattern)
DB_CONFIG = {
    'host': os.getenv('OMOP_DB_HOST'),
    'port': os.getenv('OMOP_DB_PORT'),
    'database': os.getenv('OMOP_DB_NAME'),
    'user': os.getenv('OMOP_DB_USER'),        # Regular OMOP user
    'password': os.getenv('OMOP_DB_PASSWORD') # OMOP user password
}

# Vocabulary files in official OHDSI loading order
# Sources:
# - OHDSI/ETL-Synthea: https://github.com/OHDSI/ETL-Synthea/blob/main/R/LoadVocabFromCsv.r
# - sidataplus community: https://github.com/sidataplus/omop-vocab-loader/blob/main/load_vocab.py
VOCABULARY_FILES = [
    'CONCEPT.csv',           # 1. Central metadata table (must be first)
    'VOCABULARY.csv',        # 2. References concept
    'CONCEPT_ANCESTOR.csv',  # 3. Hierarchical relationships
    'CONCEPT_RELATIONSHIP.csv', # 4. Concept relationships
    'RELATIONSHIP.csv',      # 5. Relationship types
    'CONCEPT_SYNONYM.csv',   # 6. Alternative names
    'DOMAIN.csv',           # 7. Domain definitions
    'CONCEPT_CLASS.csv',    # 8. Concept classes
    'DRUG_STRENGTH.csv'     # 9. Drug strength data
]

def get_vocabulary_path():
    """Get vocabulary directory path from environment or auto-detect."""

    # First, check if explicitly set in environment
    env_path = os.getenv('VOCABULARY_PATH')
    if env_path:
        vocab_path = Path(env_path)
        if vocab_path.exists():
            return vocab_path
        else:
            raise FileNotFoundError(f"Environment VOCABULARY_PATH not found: {vocab_path}")

    # Auto-detect: find most recent omop_v5_* directory
    vocab_base_dir = Path('./data/vocabulary')
    if not vocab_base_dir.exists():
        raise FileNotFoundError(f"Vocabulary base directory not found: {vocab_base_dir}")

    # Find all directories matching omop_v5_* pattern
    omop_dirs = list(vocab_base_dir.glob('omop_v5_*'))

    if not omop_dirs:
        raise FileNotFoundError(f"No omop_v5_* directories found in {vocab_base_dir}")

    # Sort by name (which includes date) and take the most recent
    most_recent = sorted(omop_dirs)[-1]

    print(f"📁 Auto-detected vocabulary directory: {most_recent}")
    return most_recent

def process_csv_official_pattern(connection, csv_file, table_name, vocab_path, chunk_size=1000000):
    """
    Load CSV file using official OHDSI pattern: DELETE FROM + chunked INSERT.

    This follows the exact pattern used by:
    - OHDSI/ETL-Synthea (R): DELETE FROM + insertTable()
    - sidataplus/omop-vocab-loader (Python): DELETE FROM + execute_values()
    - OHDSI Forums recommendation: No constraint disabling required

    Args:
        connection: psycopg2 connection object
        csv_file: Name of CSV file to load
        table_name: Target table name
        vocab_path: Path to vocabulary directory
        chunk_size: Number of rows to process per chunk (default: 1M)

    Returns:
        int: Number of rows loaded
    """
    file_path = vocab_path / csv_file
    if not file_path.exists():
        print(f"⚠️  File not found: {csv_file} - Skipping")
        return 0

    print(f"📁 Working on file {file_path}")
    start_time = time.time()

    # Count total lines for progress tracking
    total_lines = sum(1 for _ in open(file_path, 'r', encoding='utf-8'))
    print(f"📊 Total lines: {total_lines:,}")

    processed_lines = 0
    total_loaded = 0

    try:
        with connection.cursor() as cur:
            # Step 1: DELETE FROM table (official OHDSI pattern)
            print(f"🗑️  Clearing table {table_name}...")
            cur.execute(f"DELETE FROM {table_name};")
            print(f"✅ Table {table_name} cleared")

            # Step 2: Load data in chunks using pandas + execute_values
            print(f"📥 Loading data in chunks of {chunk_size:,} rows...")

            for chunk in pd.read_csv(
                file_path,
                sep='\t',
                dtype=str,
                keep_default_na=False,
                na_values="",
                encoding='utf-8',
                chunksize=chunk_size
            ):
                # Handle date columns for specific tables (official OHDSI pattern)
                if csv_file.lower() in ["concept.csv", "concept_relationship.csv", "drug_strength.csv"]:
                    if 'valid_start_date' in chunk.columns:
                        chunk['valid_start_date'] = pd.to_datetime(chunk['valid_start_date'], format='%Y%m%d')
                    if 'valid_end_date' in chunk.columns:
                        chunk['valid_end_date'] = pd.to_datetime(chunk['valid_end_date'], format='%Y%m%d')

                # Handle drug_strength specific null replacements (official OHDSI pattern)
                if csv_file.lower() == "drug_strength.csv":
                    columns_to_replace_na = [
                        "amount_value", "amount_unit_concept_id", "numerator_value",
                        "numerator_unit_concept_id", "denominator_value",
                        "denominator_unit_concept_id", "box_size"
                    ]
                    for col in columns_to_replace_na:
                        if col in chunk.columns:
                            chunk[col] = chunk[col].fillna(0)

                # Convert to tuples for execute_values
                chunk = chunk.fillna(np.nan).replace([np.nan], [None])
                tuples = [tuple(x) for x in chunk.to_numpy()]
                cols = ','.join(list(chunk.columns))

                # Insert using execute_values (community standard)
                query = f"INSERT INTO {table_name}({cols}) VALUES %s"
                psycopg2.extras.execute_values(
                    cur, query, tuples, template=None, page_size=1000
                )

                processed_lines += len(chunk)
                total_loaded += len(chunk)
                remaining_lines = total_lines - processed_lines
                print(f"📈 Processed: {processed_lines:,}, Remaining: {remaining_lines:,}")

            connection.commit()

    except Exception as e:
        print(f"❌ Error processing {csv_file}: {e}")
        connection.rollback()
        return 0

    end_time = time.time()
    elapsed_time = end_time - start_time
    rate = total_loaded / elapsed_time if elapsed_time > 0 else 0

    print(f"✅ Finished processing {csv_file}")
    print(f"📊 Loaded {total_loaded:,} rows in {elapsed_time:.1f}s ({rate:,.0f} rows/sec)")

    return total_loaded

def main():
    """
    Main vocabulary loading process using official OHDSI pattern.

    This implementation follows the official OHDSI pattern validated by:
    - OHDSI/ETL-Synthea (R implementation)
    - sidataplus/omop-vocab-loader (Python community implementation)
    - OHDSI Forums official guidance (Eduard Korchmar, November 2023)

    Key differences from previous implementation:
    - NO constraint disabling (follows official OHDSI guidance)
    - Uses DELETE FROM + INSERT pattern (same as official R method)
    - Works with regular database user (no superuser required)
    - Uses pandas chunking with execute_values() for performance
    """

    print("🚀 OMOP Vocabulary Loader Starting (Official OHDSI Pattern)...")
    print("=" * 70)
    print("📋 Method: DELETE FROM + chunked INSERT (official OHDSI pattern)")
    print("📋 Source: OHDSI/ETL-Synthea + sidataplus community implementation")
    print("📋 Guidance: OHDSI Forums (https://forums.ohdsi.org/t/foreign-key-constraints-issue/20462)")
    print("=" * 70)

    # Verify vocabulary directory
    try:
        vocab_path = get_vocabulary_path()
        print(f"📂 Vocabulary directory: {vocab_path}")
    except FileNotFoundError as e:
        print(f"❌ {e}")
        sys.exit(1)

    # Connect to database
    try:
        print(f"🔌 Connecting to database: {DB_CONFIG['database']} as user: {DB_CONFIG['user']}")
        conn = psycopg2.connect(**DB_CONFIG)
        # Note: No autocommit - we handle transactions manually for better control
        print("✅ Database connection established")
        print("✅ Using regular database user (no superuser required)")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Tip: Verify OMOP_DB_USER and OMOP_DB_PASSWORD in .env file")
        sys.exit(1)

    print("=" * 70)
    print("� Loading vocabulary files using official OHDSI pattern...")
    print("📋 Pattern: DELETE FROM table → Load data in chunks → Commit")
    print("=" * 70)

    # Load vocabulary files in official OHDSI order
    total_loaded = 0
    start_time = time.time()

    for csv_file in VOCABULARY_FILES:
        table_name = csv_file.replace('.csv', '').lower()
        print(f"\n📁 Processing {csv_file} → {table_name}")
        rows_loaded = process_csv_official_pattern(conn, csv_file, table_name, vocab_path)
        total_loaded += rows_loaded
        print()  # Empty line for readability
    
    # Final summary
    total_time = time.time() - start_time
    print("=" * 70)
    print(f"🎉 Vocabulary loading completed using official OHDSI pattern!")
    print(f"📊 Total records loaded: {total_loaded:,}")
    print(f"⏱️  Total time: {total_time:.1f} seconds")
    print(f"🚀 Average rate: {total_loaded/total_time:,.0f} records/sec")

    # Verify key tables
    print("\n🔍 Verifying vocabulary tables:")
    try:
        with conn.cursor() as cur:
            for csv_file in VOCABULARY_FILES[:4]:  # Check first 4 tables
                table_name = csv_file.replace('.csv', '').lower()
                try:
                    cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cur.fetchone()[0]
                    print(f"✅ {table_name}: {count:,} records")
                except Exception as e:
                    print(f"❌ {table_name}: Error - {e}")
    except Exception as e:
        print(f"⚠️  Warning: Could not verify tables: {e}")

    # Close connection
    conn.close()
    print("\n✅ Official OHDSI vocabulary loading pattern completed successfully!")
    print("📋 Method used: DELETE FROM + chunked INSERT (no constraint disabling)")
    print("📋 Source validation: OHDSI/ETL-Synthea + sidataplus + OHDSI Forums")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
OMOP Vocabulary Loader Script - Official OHDSI Forum Recommendation

This script implements the OFFICIAL OHDSI recommendation for handling circular 
foreign key dependencies in vocabulary loading.

Official OHDSI Forum Recommendation:
Source: https://forums.ohdsi.org/t/foreign-key-constraints-issue/20462
Author: <PERSON> (OHDSI Community Expert)
Date: November 2023

Quote: "Usual workflow is to create all constraints after all the data had been 
uploaded in corresponding tables. I would suggest to drop all constraints and 
re-run DDL for them after the successful upload."

Technical Approach (Official OHDSI Pattern):
1. Drop all foreign key constraints on vocabulary tables
2. Load all vocabulary data using efficient bulk loading
3. Re-create all foreign key constraints

This solves the circular dependency problem officially recognized by OHDSI:
"OMOP CDM is not a fully normalized model, and by design contains cyclical 
foreign key references. It does make inserts and uploads tricky."

Usage:
    python scripts/load_vocabularies_official_ohdsi.py

Requirements:
    - Athena vocabulary files in data/vocabulary/omop_v5_* directory
    - PostgreSQL OMOP database configured in .env
    - pandas and psycopg2-binary packages installed
    - Database user with DDL privileges (to drop/create constraints)
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
import pandas as pd
import numpy as np
import psycopg2
import psycopg2.extras

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('OMOP_DB_HOST'),
    'port': os.getenv('OMOP_DB_PORT'),
    'database': os.getenv('OMOP_DB_NAME'),
    'user': os.getenv('OMOP_DB_USER'),
    'password': os.getenv('OMOP_DB_PASSWORD')
}

# Vocabulary files in official OHDSI loading order
VOCABULARY_FILES = [
    'CONCEPT.csv',           # 1. Central metadata table
    'VOCABULARY.csv',        # 2. Vocabulary definitions
    'CONCEPT_ANCESTOR.csv',  # 3. Hierarchical relationships
    'CONCEPT_RELATIONSHIP.csv', # 4. Concept relationships
    'RELATIONSHIP.csv',      # 5. Relationship types
    'CONCEPT_SYNONYM.csv',   # 6. Alternative names
    'DOMAIN.csv',           # 7. Domain definitions
    'CONCEPT_CLASS.csv',    # 8. Concept classes
    'DRUG_STRENGTH.csv'     # 9. Drug strength data
]

# Foreign key constraints to drop and recreate
# These are the constraints that cause circular dependency issues
VOCABULARY_CONSTRAINTS = [
    # Concept table constraints
    "ALTER TABLE concept DROP CONSTRAINT IF EXISTS fpk_concept_domain_id",
    "ALTER TABLE concept DROP CONSTRAINT IF EXISTS fpk_concept_vocabulary_id", 
    "ALTER TABLE concept DROP CONSTRAINT IF EXISTS fpk_concept_concept_class_id",
    
    # Vocabulary table constraints
    "ALTER TABLE vocabulary DROP CONSTRAINT IF EXISTS fpk_vocabulary_vocabulary_concept_id",
    
    # Domain table constraints  
    "ALTER TABLE domain DROP CONSTRAINT IF EXISTS fpk_domain_domain_concept_id",
    
    # Concept_class table constraints
    "ALTER TABLE concept_class DROP CONSTRAINT IF EXISTS fpk_concept_class_concept_class_concept_id",
    
    # Relationship table constraints
    "ALTER TABLE relationship DROP CONSTRAINT IF EXISTS fpk_relationship_relationship_concept_id",
    "ALTER TABLE relationship DROP CONSTRAINT IF EXISTS fpk_relationship_reverse_relationship_id",
    
    # Concept_relationship table constraints
    "ALTER TABLE concept_relationship DROP CONSTRAINT IF EXISTS fpk_concept_relationship_concept_id_1",
    "ALTER TABLE concept_relationship DROP CONSTRAINT IF EXISTS fpk_concept_relationship_concept_id_2",
    "ALTER TABLE concept_relationship DROP CONSTRAINT IF EXISTS fpk_concept_relationship_relationship_id",
    
    # Concept_ancestor table constraints
    "ALTER TABLE concept_ancestor DROP CONSTRAINT IF EXISTS fpk_concept_ancestor_ancestor_concept_id",
    "ALTER TABLE concept_ancestor DROP CONSTRAINT IF EXISTS fpk_concept_ancestor_descendant_concept_id",
    
    # Concept_synonym table constraints
    "ALTER TABLE concept_synonym DROP CONSTRAINT IF EXISTS fpk_concept_synonym_concept_id",
    
    # Drug_strength table constraints
    "ALTER TABLE drug_strength DROP CONSTRAINT IF EXISTS fpk_drug_strength_drug_concept_id",
    "ALTER TABLE drug_strength DROP CONSTRAINT IF EXISTS fpk_drug_strength_ingredient_concept_id",
    "ALTER TABLE drug_strength DROP CONSTRAINT IF EXISTS fpk_drug_strength_amount_unit_concept_id",
    "ALTER TABLE drug_strength DROP CONSTRAINT IF EXISTS fpk_drug_strength_numerator_unit_concept_id",
    "ALTER TABLE drug_strength DROP CONSTRAINT IF EXISTS fpk_drug_strength_denominator_unit_concept_id"
]

def get_vocabulary_path():
    """Get vocabulary directory path from environment or auto-detect."""
    
    # First, check if explicitly set in environment
    env_path = os.getenv('VOCABULARY_PATH')
    if env_path:
        vocab_path = Path(env_path)
        if vocab_path.exists():
            return vocab_path
        else:
            raise FileNotFoundError(f"Environment VOCABULARY_PATH not found: {vocab_path}")

    # Auto-detect: find most recent omop_v5_* directory
    vocab_base_dir = Path('./data/vocabulary')
    if not vocab_base_dir.exists():
        raise FileNotFoundError(f"Vocabulary base directory not found: {vocab_base_dir}")

    # Find all directories matching omop_v5_* pattern
    omop_dirs = list(vocab_base_dir.glob('omop_v5_*'))

    if not omop_dirs:
        raise FileNotFoundError(f"No omop_v5_* directories found in {vocab_base_dir}")

    # Sort by name (which includes date) and take the most recent
    most_recent = sorted(omop_dirs)[-1]

    print(f"📁 Auto-detected vocabulary directory: {most_recent}")
    return most_recent

def drop_vocabulary_constraints(connection):
    """
    Drop all foreign key constraints that cause circular dependencies.
    
    This implements the official OHDSI recommendation:
    "drop all constraints and re-run DDL for them after the successful upload"
    """
    print("🔓 Dropping foreign key constraints (Official OHDSI recommendation)...")
    
    try:
        with connection.cursor() as cur:
            for constraint_sql in VOCABULARY_CONSTRAINTS:
                try:
                    cur.execute(constraint_sql)
                    print(f"✅ {constraint_sql}")
                except Exception as e:
                    print(f"⚠️  {constraint_sql} - {e}")
            
            connection.commit()
            print("✅ All vocabulary foreign key constraints dropped")
            
    except Exception as e:
        print(f"❌ Error dropping constraints: {e}")
        raise

def recreate_vocabulary_constraints(connection):
    """
    Re-create all foreign key constraints after data loading.
    
    This completes the official OHDSI recommendation workflow.
    """
    print("🔒 Re-creating foreign key constraints...")
    
    # DDL commands to recreate constraints
    # These come from the official OMOP CDM DDL
    recreate_constraints = [
        # Concept table constraints
        "ALTER TABLE concept ADD CONSTRAINT fpk_concept_domain_id FOREIGN KEY (domain_id) REFERENCES domain (domain_id)",
        "ALTER TABLE concept ADD CONSTRAINT fpk_concept_vocabulary_id FOREIGN KEY (vocabulary_id) REFERENCES vocabulary (vocabulary_id)",
        "ALTER TABLE concept ADD CONSTRAINT fpk_concept_concept_class_id FOREIGN KEY (concept_class_id) REFERENCES concept_class (concept_class_id)",
        
        # Vocabulary table constraints
        "ALTER TABLE vocabulary ADD CONSTRAINT fpk_vocabulary_vocabulary_concept_id FOREIGN KEY (vocabulary_concept_id) REFERENCES concept (concept_id)",
        
        # Domain table constraints
        "ALTER TABLE domain ADD CONSTRAINT fpk_domain_domain_concept_id FOREIGN KEY (domain_concept_id) REFERENCES concept (concept_id)",
        
        # Concept_class table constraints
        "ALTER TABLE concept_class ADD CONSTRAINT fpk_concept_class_concept_class_concept_id FOREIGN KEY (concept_class_concept_id) REFERENCES concept (concept_id)",
        
        # Relationship table constraints
        "ALTER TABLE relationship ADD CONSTRAINT fpk_relationship_relationship_concept_id FOREIGN KEY (relationship_concept_id) REFERENCES concept (concept_id)",
        "ALTER TABLE relationship ADD CONSTRAINT fpk_relationship_reverse_relationship_id FOREIGN KEY (reverse_relationship_id) REFERENCES relationship (relationship_id)",
        
        # Concept_relationship table constraints
        "ALTER TABLE concept_relationship ADD CONSTRAINT fpk_concept_relationship_concept_id_1 FOREIGN KEY (concept_id_1) REFERENCES concept (concept_id)",
        "ALTER TABLE concept_relationship ADD CONSTRAINT fpk_concept_relationship_concept_id_2 FOREIGN KEY (concept_id_2) REFERENCES concept (concept_id)",
        "ALTER TABLE concept_relationship ADD CONSTRAINT fpk_concept_relationship_relationship_id FOREIGN KEY (relationship_id) REFERENCES relationship (relationship_id)",
        
        # Concept_ancestor table constraints
        "ALTER TABLE concept_ancestor ADD CONSTRAINT fpk_concept_ancestor_ancestor_concept_id FOREIGN KEY (ancestor_concept_id) REFERENCES concept (concept_id)",
        "ALTER TABLE concept_ancestor ADD CONSTRAINT fpk_concept_ancestor_descendant_concept_id FOREIGN KEY (descendant_concept_id) REFERENCES concept (concept_id)",
        
        # Concept_synonym table constraints
        "ALTER TABLE concept_synonym ADD CONSTRAINT fpk_concept_synonym_concept_id FOREIGN KEY (concept_id) REFERENCES concept (concept_id)",
        
        # Drug_strength table constraints
        "ALTER TABLE drug_strength ADD CONSTRAINT fpk_drug_strength_drug_concept_id FOREIGN KEY (drug_concept_id) REFERENCES concept (concept_id)",
        "ALTER TABLE drug_strength ADD CONSTRAINT fpk_drug_strength_ingredient_concept_id FOREIGN KEY (ingredient_concept_id) REFERENCES concept (concept_id)",
        "ALTER TABLE drug_strength ADD CONSTRAINT fpk_drug_strength_amount_unit_concept_id FOREIGN KEY (amount_unit_concept_id) REFERENCES concept (concept_id)",
        "ALTER TABLE drug_strength ADD CONSTRAINT fpk_drug_strength_numerator_unit_concept_id FOREIGN KEY (numerator_unit_concept_id) REFERENCES concept (concept_id)",
        "ALTER TABLE drug_strength ADD CONSTRAINT fpk_drug_strength_denominator_unit_concept_id FOREIGN KEY (denominator_unit_concept_id) REFERENCES concept (concept_id)"
    ]
    
    try:
        with connection.cursor() as cur:
            for constraint_sql in recreate_constraints:
                try:
                    cur.execute(constraint_sql)
                    print(f"✅ {constraint_sql}")
                except Exception as e:
                    print(f"⚠️  {constraint_sql} - {e}")
            
            connection.commit()
            print("✅ All vocabulary foreign key constraints re-created")
            
    except Exception as e:
        print(f"❌ Error re-creating constraints: {e}")
        raise

def process_csv_official_ohdsi(connection, csv_file, table_name, vocab_path, chunk_size=1000000):
    """
    Load CSV file using official OHDSI pattern with constraints already dropped.
    
    Since constraints are dropped, we can use simple TRUNCATE + INSERT pattern.
    """
    file_path = vocab_path / csv_file
    if not file_path.exists():
        print(f"⚠️  File not found: {csv_file} - Skipping")
        return 0
    
    print(f"📁 Working on file {file_path}")
    start_time = time.time()
    
    # Count total lines for progress tracking
    total_lines = sum(1 for _ in open(file_path, 'r', encoding='utf-8'))
    print(f"📊 Total lines: {total_lines:,}")
    
    processed_lines = 0
    total_loaded = 0
    
    try:
        with connection.cursor() as cur:
            # Step 1: TRUNCATE table (faster than DELETE when no constraints)
            print(f"🗑️  Truncating table {table_name}...")
            cur.execute(f"TRUNCATE TABLE {table_name};")
            print(f"✅ Table {table_name} truncated")
            
            # Step 2: Load data in chunks using pandas + execute_values
            print(f"📥 Loading data in chunks of {chunk_size:,} rows...")
            
            for chunk in pd.read_csv(
                file_path,
                sep='\t',
                dtype=str,
                keep_default_na=False,
                na_values="",
                encoding='utf-8',
                chunksize=chunk_size
            ):
                # Handle date columns for specific tables (official OHDSI pattern)
                if csv_file.lower() in ["concept.csv", "concept_relationship.csv", "drug_strength.csv"]:
                    if 'valid_start_date' in chunk.columns:
                        chunk['valid_start_date'] = pd.to_datetime(chunk['valid_start_date'], format='%Y%m%d')
                    if 'valid_end_date' in chunk.columns:
                        chunk['valid_end_date'] = pd.to_datetime(chunk['valid_end_date'], format='%Y%m%d')
                
                # Handle drug_strength specific null replacements (official OHDSI pattern)
                if csv_file.lower() == "drug_strength.csv":
                    columns_to_replace_na = [
                        "amount_value", "amount_unit_concept_id", "numerator_value",
                        "numerator_unit_concept_id", "denominator_value", 
                        "denominator_unit_concept_id", "box_size"
                    ]
                    for col in columns_to_replace_na:
                        if col in chunk.columns:
                            chunk[col] = chunk[col].fillna(0)
                
                # Convert to tuples for execute_values
                chunk = chunk.fillna(np.nan).replace([np.nan], [None])
                tuples = [tuple(x) for x in chunk.to_numpy()]
                cols = ','.join(list(chunk.columns))
                
                # Insert using execute_values (community standard)
                query = f"INSERT INTO {table_name}({cols}) VALUES %s"
                psycopg2.extras.execute_values(
                    cur, query, tuples, template=None, page_size=1000
                )
                
                processed_lines += len(chunk)
                total_loaded += len(chunk)
                remaining_lines = total_lines - processed_lines
                print(f"📈 Processed: {processed_lines:,}, Remaining: {remaining_lines:,}")
            
            connection.commit()
            
    except Exception as e:
        print(f"❌ Error processing {csv_file}: {e}")
        connection.rollback()
        return 0
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    elapsed_minutes = elapsed_time / 60
    rate = total_loaded / elapsed_time if elapsed_time > 0 else 0
    
    print(f"✅ Finished processing {csv_file}")
    print(f"📊 Loaded {total_loaded:,} rows in {elapsed_time:.1f}s ({elapsed_minutes:.1f} min) - {rate:,.0f} rows/sec")
    
    return total_loaded

def main():
    """
    Main vocabulary loading process using official OHDSI forum recommendation.

    Implements the complete workflow recommended by Eduard Korchmar:
    1. Drop all foreign key constraints
    2. Load all vocabulary data
    3. Re-create all foreign key constraints
    """

    print("🚀 OMOP Vocabulary Loader - Official OHDSI Forum Recommendation")
    print("=" * 80)
    print("📋 Method: Drop constraints → Load data → Re-create constraints")
    print("📋 Source: OHDSI Forums (Eduard Korchmar, November 2023)")
    print("📋 URL: https://forums.ohdsi.org/t/foreign-key-constraints-issue/20462")
    print("=" * 80)

    # Verify vocabulary directory
    try:
        vocab_path = get_vocabulary_path()
        print(f"📂 Vocabulary directory: {vocab_path}")
    except FileNotFoundError as e:
        print(f"❌ {e}")
        sys.exit(1)

    # Connect to database
    try:
        print(f"🔌 Connecting to database: {DB_CONFIG['database']} as user: {DB_CONFIG['user']}")
        conn = psycopg2.connect(**DB_CONFIG)
        print("✅ Database connection established")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("💡 Tip: Verify OMOP_DB_USER and OMOP_DB_PASSWORD in .env file")
        sys.exit(1)

    try:
        # Step 1: Drop foreign key constraints (Official OHDSI recommendation)
        print("\n" + "=" * 80)
        print("📋 STEP 1: Drop foreign key constraints")
        print("=" * 80)
        drop_vocabulary_constraints(conn)

        # Step 2: Load vocabulary data
        print("\n" + "=" * 80)
        print("📋 STEP 2: Load vocabulary data (no constraint checking)")
        print("=" * 80)

        total_loaded = 0
        start_time = time.time()

        for csv_file in VOCABULARY_FILES:
            table_name = csv_file.replace('.csv', '').lower()
            print(f"\n📁 Processing {csv_file} → {table_name}")
            rows_loaded = process_csv_official_ohdsi(conn, csv_file, table_name, vocab_path)
            total_loaded += rows_loaded
            print()  # Empty line for readability

        # Step 3: Re-create foreign key constraints
        print("\n" + "=" * 80)
        print("📋 STEP 3: Re-create foreign key constraints")
        print("=" * 80)
        recreate_vocabulary_constraints(conn)

        # Final summary
        total_time = time.time() - start_time
        total_minutes = total_time / 60
        print("\n" + "=" * 80)
        print(f"🎉 Official OHDSI vocabulary loading completed successfully!")
        print(f"📊 Total records loaded: {total_loaded:,}")
        print(f"⏱️  Total time: {total_time:.1f} seconds ({total_minutes:.1f} minutes)")
        print(f"🚀 Average rate: {total_loaded/total_time:,.0f} records/sec")

        # Verify key tables
        print("\n🔍 Verifying vocabulary tables:")
        try:
            with conn.cursor() as cur:
                for csv_file in VOCABULARY_FILES[:4]:  # Check first 4 tables
                    table_name = csv_file.replace('.csv', '').lower()
                    try:
                        cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cur.fetchone()[0]
                        print(f"✅ {table_name}: {count:,} records")
                    except Exception as e:
                        print(f"❌ {table_name}: Error - {e}")
        except Exception as e:
            print(f"⚠️  Warning: Could not verify tables: {e}")

        print("\n✅ Official OHDSI vocabulary loading workflow completed!")
        print("📋 Method: Drop constraints → Load data → Re-create constraints")
        print("📋 Source: OHDSI Forums official recommendation")

    except Exception as e:
        print(f"\n❌ Critical error during vocabulary loading: {e}")
        print("🔄 Attempting to re-create constraints for safety...")
        try:
            recreate_vocabulary_constraints(conn)
        except:
            print("⚠️  Could not re-create constraints. Manual intervention may be required.")
        sys.exit(1)

    finally:
        # Close connection
        conn.close()

if __name__ == "__main__":
    main()
